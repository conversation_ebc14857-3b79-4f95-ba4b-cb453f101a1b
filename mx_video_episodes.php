<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);


$host = 'localhost';
$user = 'root';
$pass = 'apps8888';
$dbname = 'movie_site';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(["error" => "Database connection failed", "details" => $e->getMessage()]);
    exit;
}

// Read raw input from Node.js
$input = json_decode(file_get_contents('php://input'), true);

// Extract values safely
$movie_name = isset($input['movie_name']) ? $input['movie_name'] : null;
$num_episodes = isset($input['num_episodes']) ? (int) $input['num_episodes'] : null;
$movie_id = isset($input['movie_id']) ? (int) $input['movie_id'] : null;

echo $movie_name;
echo $movie_id;
echo $num_episodes;

// Validate input
if (!$movie_name || !$num_episodes || $num_episodes < 1 || !$movie_id) {
    http_response_code(400);
    echo json_encode(["error" => "Invalid or missing movie_name or num_episodes"]);
    exit;
}

try {
    $stmt = $pdo->prepare("SELECT title FROM post_tbl WHERE id = ?");
    $stmt->execute([$movie_id]);
    $movie = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$movie) {
        http_response_code(404);
        echo json_encode(["error" => "Movie not found for ID: $movie_id"]);
        exit;
    }

    $movie_title = $movie['title'];
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(["error" => "Failed to fetch movie title", "details" => $e->getMessage()]);
    exit;
}

// Generate episode links
function generateAndStoreLinks($movie_name, $num_episodes, $movie_id, $pdo, $movie_title)
{
    $response = [];
    $base_url = "https://1401972946.rsc.cdn77.org/shortdrama/";

    try {
        $stmt = $pdo->prepare("INSERT INTO mxvideo (title, link,epno, movieid, sub_title) VALUES (?, ?, ?, ?,?)");

        for ($i = 1; $i <= $num_episodes; $i++) {
            $video_url = $base_url . $movie_name . "/Ep" . $i . ".mp4";
            $srt_url = $base_url . $movie_name . "/SRT/Ep" . $i . ".srt";

            $stmt->execute([$movie_title, $video_url, $i, $movie_id, $srt_url]);
            $response[] = [
                "episode" => $i,
                "url" => $video_url,
                "srt_url" => $srt_url,
                "movie_id" => $movie_id,
                "title" => $movie_title,
                "status" => "success"
            ];
        }
        return $response;
    } catch (PDOException $e) {
        throw $e;
    }

}

try {

    $result = generateAndStoreLinks($movie_name, $num_episodes, $movie_id, $pdo, $movie_title);

    // Output JSON
    header('Content-Type: application/json');
    echo json_encode([
        "status" => "success",
        "data" => $result
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(["error" => "Failed to store links", "details" => $e->getMessage()]);
}

