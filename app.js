const express = require("express");
const cookieParser = require("cookie-parser");
const cluster = require("cluster");
const os = require("os");
const bodyParser = require("body-parser");
const multer = require("multer");
const form = multer();
const router = require("./routes/router");
const cors = require("cors");
var https = require("https");
const session = require("express-session");
const session1 = require("express-session");
const passport = require("passport");
const GoogleStrategy = require("passport-google-oauth20").Strategy;
var fs = require("fs");
const axios = require("axios");
const querystring = require("querystring");
const crypto = require("crypto");
var CryptoJS = require("crypto-js");
const numCPUs = os.cpus().length;
const base64url = require("base64url");
const cron = require("node-cron");
const { google } = require("googleapis");
const { authenticate } = require("@google-cloud/local-auth");
const mysql = require("mysql");
const { lookup } = require("geoip-lite");
const SCOPES = [
  "https://www.googleapis.com/auth/drive.file",
  "https://www.googleapis.com/auth/drive",
  "https://www.googleapis.com/auth/drive.file",
  "https://www.googleapis.com/auth/drive.metadata",
];

if (cluster.isMaster) {
  console.log(`Master process ${process.pid} is running`);

  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  var connection = mysql.createConnection({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.DB,
    multipleStatements: true,
  });
  connection.connect();

  cron.schedule(
    "00 09 * * *",
    () => {
      try {
        console.log("Received request for AdminCheckEmbeded");
        // let offset = req.params["page"];
        const limit = 8;
        //  offset = (offset - 1) * limit;

        connection.query(
          `select post_tbl.source_name as user_name,post_tbl.source_id as user_id ,post_tbl.id as post_id,concat('${process.env.PUBLIC_PROFILE_PATH}',post_tbl.source_profile) as profile,post_tbl.link as link,post_tbl.trailer_link as trailer,concat('${process.env.PUBLIC_POST_PATH}',post_tbl.thumbnail) as thumbnail,post_tbl.id,post_tbl.source_id,post_tbl.title,post_tbl.ismovie,post_tbl.created_date,post_tbl.google_rating,post_tbl.imdb_rating,post_tbl.season,post_tbl.caption,post_tbl.views,genre_tbl.genre as genre,group_concat(distinct language_tbl.language) as language,group_concat(distinct category_tbl.cat_name) as category from post_tbl INNER JOIN language_tbl ON find_in_set(language_tbl.lang_id,post_tbl.lang_id) inner join genre_tbl on post_tbl.genre_id=genre_tbl.id INNER JOIN category_tbl ON find_in_set(category_tbl.cat_id,post_tbl.cat_id) group by post_tbl.id`,
          async (err, data) => {
            if (err) {
              console.error("Error querying database:", err);
              return res.json({ status: 0, message: "Internal Server Error" });
            }

            if (data.length > 0) {
              console.log("Data retrieved from database");
              const result = JSON.parse(JSON.stringify(data));
              const regexp =
                /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;

              const batchProcess = async (batch) => {
                console.log("Processing batch of size:", batch.length);
                const requests = batch.map(async (e) => {
                  if (e.link.length > 0 && e.link.includes("youtu")) {
                    const videoid = e.link.match(regexp);
                    if (videoid != null) {
                      try {
                        const videoData = await axios.get(
                          `https://www.googleapis.com/youtube/v3/videos?id=${videoid[1]}&key=AIzaSyD-8cdDgvoQ5fqaHEmBEtnia0ovnzOkO_0&part=snippet,contentDetails,status`
                        );

                        if (
                          !videoData.data?.items[0]?.status?.embeddable ||
                          videoData?.data?.items[0]?.contentDetails
                            ?.contentRating?.ytRating === "ytAgeRestricted" ||
                          videoData.data?.items[0]?.status?.rejectionReason ==
                            "copyright" ||
                          videoData.data?.items[0]?.status?.privacyStatus ==
                            "private" ||
                          videoData?.data?.items[0]?.contentDetails
                            ?.regionRestriction?.allowed?.length > 0 ||
                          videoData?.data?.items.length == 0 ||
                          videoData.data?.items[0]?.snippet?.channelId ==
                            "UCmL1WlDI8UkXDXCXcBQN9CA" ||
                          videoData.data?.items[0]?.snippet?.channelId ==
                            "UCYauDsl-rswjQGpA0_-Z9Pw" ||
                          videoData.data?.items[0]?.snippet?.channelId ==
                            "UCnqA9rYxHah-HY2RzGXWLFg"
                        ) {
                          if (
                            videoData.data?.items[0]?.snippet?.channelId ==
                              "UCmL1WlDI8UkXDXCXcBQN9CA" ||
                            videoData.data?.items[0]?.snippet?.channelId ==
                              "UCYauDsl-rswjQGpA0_-Z9Pw" ||
                            videoData.data?.items[0]?.snippet?.channelId ==
                              "UCnqA9rYxHah-HY2RzGXWLFg"
                          ) {
                            connection.query(
                              "update post_tbl SET isUltra=? , isrestric=? where id=?",
                              ["true", "true", e.post_id],
                              (err, checkrestric) => {
                                if (err) {
                                  return res.status(500).json({
                                    status: 500,
                                    message: "Something went wrong",
                                  });
                                }
                              }
                            );
                          }

                          //age restric
                          // if (
                          //   videoData?.data?.items[0]?.contentDetails
                          //     ?.contentRating?.ytRating === "ytAgeRestricted"
                          // ) {
                          //   connection.query(
                          //     "update post_tbl SET agerestric=? where id=?",
                          //     ["true", e.post_id],
                          //     (err, checkrestric) => {
                          //       // if (err) {
                          //       //   return res.status(500).json({
                          //       //     status: 500,
                          //       //     message: "Something went wrong",
                          //       //   });
                          //       // }
                          //     }
                          //   );
                          // }

                          //copyright

                          if (videoData?.data?.items.length == 0) {
                            connection.query(
                              "update post_tbl SET ismoviecrawl=? where id=?",
                              ["true", e.post_id],
                              (err, checkrestric) => {
                                if (err) {
                                  return res.status(500).json({
                                    status: 500,
                                    message: "Something went wrong",
                                  });
                                }
                              }
                            );
                          } else {
                            connection.query(
                              "update post_tbl SET ismoviecrawl=? where id=?",
                              ["false", e.post_id],
                              (err, checkrestric) => {
                                if (err) {
                                  return res.status(500).json({
                                    status: 500,
                                    message: "Something went wrong",
                                  });
                                }
                              }
                            );
                          }

                          if (
                            !videoData.data?.items[0]?.status?.embeddable ||
                            videoData?.data?.items[0]?.contentDetails
                              ?.contentRating?.ytRating === "ytAgeRestricted"
                          ) {
                            connection.query(
                              "update post_tbl SET isembeded=? where id=?",
                              ["true", e.post_id],
                              (err, checkrestric) => {
                                if (err) {
                                  return res.status(500).json({
                                    status: 500,
                                    message: "Something went wrong",
                                  });
                                }
                              }
                            );
                          }

                          if (
                            videoData?.data?.items[0]?.contentDetails
                              ?.regionRestriction?.allowed?.length > 0
                          ) {
                            connection.query(
                              "update post_tbl SET isrestricmovie=? where id=?",
                              ["true", e.post_id],
                              (err, checkrestric) => {
                                if (err) {
                                  return res.status(500).json({
                                    status: 500,
                                    message: "Something went wrong",
                                  });
                                }
                              }
                            );
                          }

                          if (
                            videoData.data?.items[0]?.status?.rejectionReason ==
                            "copyright"
                          ) {
                            connection.query(
                              "update post_tbl SET iscopyright=? where id=?",
                              ["true", e.post_id],
                              (err, checkrestric) => {
                                if (err) {
                                  return res.status(500).json({
                                    status: 500,
                                    message: "Something went wrong",
                                  });
                                }
                              }
                            );
                          }

                          //private
                          if (
                            videoData.data?.items[0]?.status?.privacyStatus ==
                            "private"
                          ) {
                            connection.query(
                              "update post_tbl SET isprivate=? where id=?",
                              ["true", e.post_id],
                              (err, checkrestric) => {
                                if (err) {
                                  return res.status(500).json({
                                    status: 500,
                                    message: "Something went wrong",
                                  });
                                }
                              }
                            );
                          }
                          connection.query(
                            "update post_tbl SET isrestric=? where id=?",
                            ["true", e.post_id],
                            (err, checkrestric) => {
                              if (err) {
                                return res.status(500).json({
                                  status: 500,
                                  message: "Something went wrong",
                                });
                              }
                            }
                          );
                          return e;
                        } else {
                          connection.query(
                            "update post_tbl SET isrestric=? where id=?",
                            ["false", e.post_id],
                            (err, checkrestric) => {
                              if (err) {
                                return res.status(500).json({
                                  status: 500,
                                  message: "Something went wrong",
                                });
                              }
                            }
                          );
                        }
                      } catch (error) {
                        console.error("Error fetching video data:", error);
                        if (error.response && error.response.status === 403) {
                          return null;
                        }
                      }
                    }
                  }
                  return null;
                });
                return Promise.all(requests);
              };

              const batchSize = 10;
              const results = [];
              for (let i = 0; i < result.length; i += batchSize) {
                console.log(`Processing batch ${i / batchSize + 1}`);
                const batch = result.slice(i, i + batchSize);
                const batchResults = await batchProcess(batch);
                results.push(...batchResults.filter((e) => e !== null));
              }

              console.log("Total valid results:", results.length);

              if (results.length > 0) {
                // const lastpage = offset + limit >= results.length;
                console.log("Not Supported movie foundsuccesfully ");
                // return res.status(201).json({
                //   status: 1,
                //   message: "Post Found",
                //   result: results,
                //   lastpage,
                //   records: results.length,
                // });
              } else {
                console.log("Not Supported movie not found");
                // return res.status(406).json({
                //   status: 0,
                //   message: "Post Not Found",
                // });
              }
            } else {
              console.log("Not Supported movie not found");
              // return res.status(406).json({
              //   status: 0,
              //   message: "Post Not Found",
              // });
            }
          }
        );
      } catch (err) {
        console.error("Internal Server Error:", err);
      }
    },
    {
      scheduled: true,
      timezone: "Asia/Kolkata",
    }
  );

  cron.schedule(
    "57 14 * * *",
    () => {
      try {
        console.log("Received request for AdminCheckEmbeded");
        // let offset = req.params["page"];
        const limit = 8;
        //  offset = (offset - 1) * limit;

        connection.query(
          `select post_tbl.source_name as user_name,post_tbl.source_id as user_id ,post_tbl.id as post_id,concat('${process.env.PUBLIC_PROFILE_PATH}',post_tbl.source_profile) as profile,post_tbl.link as link,post_tbl.trailer_link as trailer,concat('${process.env.PUBLIC_POST_PATH}',post_tbl.thumbnail) as thumbnail,post_tbl.id,post_tbl.source_id,post_tbl.title,post_tbl.ismovie,post_tbl.created_date,post_tbl.google_rating,post_tbl.imdb_rating,post_tbl.season,post_tbl.caption,post_tbl.views,genre_tbl.genre as genre,group_concat(distinct language_tbl.language) as language,group_concat(distinct category_tbl.cat_name) as category from post_tbl INNER JOIN language_tbl ON find_in_set(language_tbl.lang_id,post_tbl.lang_id) inner join genre_tbl on post_tbl.genre_id=genre_tbl.id INNER JOIN category_tbl ON find_in_set(category_tbl.cat_id,post_tbl.cat_id) group by post_tbl.id`,
          async (err, data) => {
            if (err) {
              console.error("Error querying database:", err);
              return res.json({ status: 0, message: "Internal Server Error" });
            }

            if (data.length > 0) {
              console.log("Data retrieved from database");
              const result = JSON.parse(JSON.stringify(data));

              const batchProcess = async (batch) => {
                console.log("Processing batch of size:", batch.length);
                const requests = batch.map(async (e) => {
                  if (e.link.length > 0 && e.link.includes("dailymotion")) {
                    const videoid = e.link.split(
                      "https://www.dailymotion.com/video/"
                    );
                    if (videoid != null) {
                      try {
                        const videoData = await axios.get(
                          `https://api.dailymotion.com/video/${videoid[1]}`
                        );
                        console.log(
                          "videoid in dailymotion is",

                          videoData.data
                        );
                        if (videoData?.data?.error?.code == 404) {
                          console.log(
                            "videoid in dailymotion is",

                            videoData.data
                          );
                          connection.query(
                            "update post_tbl SET ismoviecrawl=? where id=?",
                            ["true", e.post_id],
                            (err, checkrestric) => {
                              if (err) {
                                return res.status(500).json({
                                  status: 500,
                                  message: "Something went wrong",
                                });
                              }
                            }
                          );
                          return e;
                        } else {
                          connection.query(
                            "update post_tbl SET ismoviecrawl=? where id=?",
                            ["false", e.post_id],
                            (err, checkrestric) => {
                              if (err) {
                                return res.status(500).json({
                                  status: 500,
                                  message: "Something went wrong",
                                });
                              }
                            }
                          );
                        }
                      } catch (error) {
                        if (
                          error?.response ||
                          error?.response?.status === 403 ||
                          error?.response?.status === 404
                        ) {
                          connection.query(
                            "update post_tbl SET ismoviecrawl=? where id=?",
                            ["true", e.post_id],
                            (err, checkrestric) => {
                              if (err) {
                                return res.status(500).json({
                                  status: 500,
                                  message: "Something went wrong",
                                });
                              }
                            }
                          );
                          return null;
                        }
                      }
                    }
                  }
                  return null;
                });
                return Promise.all(requests);
              };

              const batchSize = 10;
              const results = [];
              for (let i = 0; i < result.length; i += batchSize) {
                console.log(`Processing batch ${i / batchSize + 1}`);
                const batch = result.slice(i, i + batchSize);
                const batchResults = await batchProcess(batch);
                results.push(...batchResults.filter((e) => e !== null));
              }

              console.log("Total valid results:", results.length);

              if (results.length > 0) {
                // const lastpage = offset + limit >= results.length;
                console.log("Not Supported movie foundsuccesfully ");
                // return res.status(201).json({
                //   status: 1,
                //   message: "Post Found",
                //   result: results,
                //   lastpage,
                //   records: results.length,
                // });
              } else {
                console.log("Not Supported movie not found");
                // return res.status(406).json({
                //   status: 0,
                //   message: "Post Not Found",
                // });
              }
            } else {
              console.log("Not Supported movie not found");
              // return res.status(406).json({
              //   status: 0,
              //   message: "Post Not Found",
              // });
            }
          }
        );
      } catch (err) {
        console.error("Internal Server Error:", err);
      }
    },
    {
      scheduled: true,
      timezone: "Asia/Kolkata",
    }
  );

  cron.schedule(
    "10 09 * * *",
    () => {
      try {
        console.log("Received request for AdminCheckEmbeded");
        // let offset = req.params["page"];
        const limit = 8;
        //  offset = (offset - 1) * limit;

        connection.query(
          `select post_tbl.source_name as user_name,post_tbl.source_id as user_id ,post_tbl.id as post_id,concat('${process.env.PUBLIC_PROFILE_PATH}',post_tbl.source_profile) as profile,post_tbl.link as link,post_tbl.trailer_link as trailer,concat('${process.env.PUBLIC_POST_PATH}',post_tbl.thumbnail) as thumbnail,post_tbl.id,post_tbl.source_id,post_tbl.title,post_tbl.ismovie,post_tbl.created_date,post_tbl.google_rating,post_tbl.imdb_rating,post_tbl.season,post_tbl.caption,post_tbl.views,genre_tbl.genre as genre,group_concat(distinct language_tbl.language) as language,group_concat(distinct category_tbl.cat_name) as category from post_tbl INNER JOIN language_tbl ON find_in_set(language_tbl.lang_id,post_tbl.lang_id) inner join genre_tbl on post_tbl.genre_id=genre_tbl.id INNER JOIN category_tbl ON find_in_set(category_tbl.cat_id,post_tbl.cat_id) group by post_tbl.id`,
          async (err, data) => {
            if (err) {
              console.error("Error querying database:", err);
              return res.json({ status: 0, message: "Internal Server Error" });
            }

            if (data.length > 0) {
              console.log("Data retrieved from database");
              const result = JSON.parse(JSON.stringify(data));
              const regexp =
                /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;

              const batchProcess = async (batch) => {
                console.log("Processing batch of size:", batch.length);
                const requests = batch.map(async (e) => {
                  if (e.trailer.length > 0 && e.trailer.includes("youtu")) {
                    const videoid = e.trailer.match(regexp);
                    if (videoid != null) {
                      try {
                        const videoData = await axios.get(
                          `https://www.googleapis.com/youtube/v3/videos?id=${videoid[1]}&key=AIzaSyCHWLzPxk8hO6jW-vAH4zn-pG54ihUcYa8&part=snippet,contentDetails,status`
                        );

                        if (videoData?.data?.items.length == 0) {
                          connection.query(
                            "update post_tbl SET istrailercrawl=? where id=?",
                            ["true", e.post_id],
                            (err, checkrestric) => {
                              if (err) {
                                return res.status(500).json({
                                  status: 500,
                                  message: "Something went wrong",
                                });
                              }
                            }
                          );
                          return e;
                        } else {
                          connection.query(
                            "update post_tbl SET istrailercrawl=? where id=?",
                            ["false", e.post_id],
                            (err, checkrestric) => {
                              if (err) {
                                return res.status(500).json({
                                  status: 500,
                                  message: "Something went wrong",
                                });
                              }
                            }
                          );
                        }
                      } catch (error) {
                        console.error("Error fetching video data:", error);
                        if (error.response && error.response.status === 403) {
                          return null;
                        }
                      }
                    }
                  }
                  return null;
                });
                return Promise.all(requests);
              };

              const batchSize = 10;
              const results = [];
              for (let i = 0; i < result.length; i += batchSize) {
                console.log(`Processing batch ${i / batchSize + 1}`);
                const batch = result.slice(i, i + batchSize);
                const batchResults = await batchProcess(batch);
                results.push(...batchResults.filter((e) => e !== null));
              }

              console.log("Total valid results:", results.length);

              if (results.length > 0) {
                // const lastpage = offset + limit >= results.length;
                console.log("Not Supported movie foundsuccesfully ");
                // return res.status(201).json({
                //   status: 1,
                //   message: "Post Found",
                //   result: results,
                //   lastpage,
                //   records: results.length,
                // });
              } else {
                console.log("Not Supported movie not found");
                // return res.status(406).json({
                //   status: 0,
                //   message: "Post Not Found",
                // });
              }
            } else {
              console.log("Not Supported movie not found");
              // return res.status(406).json({
              //   status: 0,
              //   message: "Post Not Found",
              // });
            }
          }
        );
      } catch (err) {
        console.error("Internal Server Error:", err);
      }
    },
    {
      scheduled: true,
      timezone: "Asia/Kolkata",
    }
  );

  cron.schedule(
    "00 15 * * *",
    () => {
      try {
        console.log("Received request for AdminCheckEmbeded");
        // let offset = req.params["page"];
        const limit = 8;
        //  offset = (offset - 1) * limit;

        connection.query(
          `select post_tbl.source_name as user_name,post_tbl.source_id as user_id ,post_tbl.id as post_id,concat('${process.env.PUBLIC_PROFILE_PATH}',post_tbl.source_profile) as profile,post_tbl.link as link,post_tbl.trailer_link as trailer,concat('${process.env.PUBLIC_POST_PATH}',post_tbl.thumbnail) as thumbnail,post_tbl.id,post_tbl.source_id,post_tbl.title,post_tbl.ismovie,post_tbl.created_date,post_tbl.google_rating,post_tbl.imdb_rating,post_tbl.season,post_tbl.caption,post_tbl.views,genre_tbl.genre as genre,group_concat(distinct language_tbl.language) as language,group_concat(distinct category_tbl.cat_name) as category from post_tbl INNER JOIN language_tbl ON find_in_set(language_tbl.lang_id,post_tbl.lang_id) inner join genre_tbl on post_tbl.genre_id=genre_tbl.id INNER JOIN category_tbl ON find_in_set(category_tbl.cat_id,post_tbl.cat_id) group by post_tbl.id`,
          async (err, data) => {
            if (err) {
              console.error("Error querying database:", err);
              return res.json({ status: 0, message: "Internal Server Error" });
            }

            if (data.length > 0) {
              console.log("Data retrieved from database");
              const result = JSON.parse(JSON.stringify(data));
              const regexp =
                /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;

              const batchProcess = async (batch) => {
                console.log("Processing batch of size:", batch.length);
                const requests = batch.map(async (e) => {
                  if (e.trailer.length > 0 && e.trailer.includes("youtu")) {
                    const videoid = e.trailer.match(regexp);
                    if (videoid != null) {
                      try {
                        const videoData = await axios.get(
                          `https://www.googleapis.com/youtube/v3/videos?id=${videoid[1]}&key=AIzaSyDfTfJCgw_bODH0ob0qkHskournypvGymU&part=snippet,contentDetails,status`
                        );

                        if (videoData?.data?.items.length == 0) {
                          connection.query(
                            "update post_tbl SET istrailercrawl=? where id=?",
                            ["true", e.post_id],
                            (err, checkrestric) => {
                              if (err) {
                                return res.status(500).json({
                                  status: 500,
                                  message: "Something went wrong",
                                });
                              }
                            }
                          );
                          return e;
                        } else {
                          connection.query(
                            "update post_tbl SET istrailercrawl=? where id=?",
                            ["false", e.post_id],
                            (err, checkrestric) => {
                              if (err) {
                                return res.status(500).json({
                                  status: 500,
                                  message: "Something went wrong",
                                });
                              }
                            }
                          );
                        }
                      } catch (error) {
                        console.error("Error fetching video data:", error);
                        if (error.response && error.response.status === 403) {
                          return null;
                        }
                      }
                    }
                  }
                  return null;
                });
                return Promise.all(requests);
              };

              const batchSize = 10;
              const results = [];
              for (let i = 0; i < result.length; i += batchSize) {
                console.log(`Processing batch ${i / batchSize + 1}`);
                const batch = result.slice(i, i + batchSize);
                const batchResults = await batchProcess(batch);
                results.push(...batchResults.filter((e) => e !== null));
              }

              console.log("Total valid results:", results.length);

              if (results.length > 0) {
                // const lastpage = offset + limit >= results.length;
                console.log("Not Supported movie foundsuccesfully ");
                // return res.status(201).json({
                //   status: 1,
                //   message: "Post Found",
                //   result: results,
                //   lastpage,
                //   records: results.length,
                // });
              } else {
                console.log("Not Supported movie not found");
                // return res.status(406).json({
                //   status: 0,
                //   message: "Post Not Found",
                // });
              }
            } else {
              console.log("Not Supported movie not found");
              // return res.status(406).json({
              //   status: 0,
              //   message: "Post Not Found",
              // });
            }
          }
        );
      } catch (err) {
        console.error("Internal Server Error:", err);
      }
    },
    {
      scheduled: true,
      timezone: "Asia/Kolkata",
    }
  );

  cron.schedule(
    "10 15 * * *",
    () => {
      try {
        console.log("Received request for AdminCheckEmbeded");
        // let offset = req.params["page"];
        const limit = 8;
        //  offset = (offset - 1) * limit;

        connection.query(
          `select post_tbl.source_name as user_name,post_tbl.source_id as user_id ,post_tbl.id as post_id,concat('${process.env.PUBLIC_PROFILE_PATH}',post_tbl.source_profile) as profile,post_tbl.link as link,post_tbl.trailer_link as trailer,concat('${process.env.PUBLIC_POST_PATH}',post_tbl.thumbnail) as thumbnail,post_tbl.id,post_tbl.source_id,post_tbl.title,post_tbl.ismovie,post_tbl.created_date,post_tbl.google_rating,post_tbl.imdb_rating,post_tbl.season,post_tbl.caption,post_tbl.views,genre_tbl.genre as genre,group_concat(distinct language_tbl.language) as language,group_concat(distinct category_tbl.cat_name) as category from post_tbl INNER JOIN language_tbl ON find_in_set(language_tbl.lang_id,post_tbl.lang_id) inner join genre_tbl on post_tbl.genre_id=genre_tbl.id INNER JOIN category_tbl ON find_in_set(category_tbl.cat_id,post_tbl.cat_id) group by post_tbl.id`,
          async (err, data) => {
            if (err) {
              console.error("Error querying database:", err);
              return res.json({ status: 0, message: "Internal Server Error" });
            }

            if (data.length > 0) {
              console.log("Data retrieved from database");
              const result = JSON.parse(JSON.stringify(data));
              const regexp =
                /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;

              const batchProcess = async (batch) => {
                console.log("Processing batch of size:", batch.length);
                const requests = batch.map(async (e) => {
                  if (e.link.length > 0 && e.link.includes("youtu")) {
                    const videoid = e.link.match(regexp);
                    if (videoid != null) {
                      try {
                        const videoData = await axios.get(
                          `https://www.googleapis.com/youtube/v3/videos?id=${videoid[1]}&key=AIzaSyCvLheHSlzpbuBiQKvAE8MjwwEdXfchBJI&part=snippet,contentDetails,status`
                        );

                        if (videoData?.data?.items.length == 0) {
                          connection.query(
                            "update post_tbl SET ismoviecrawl=? where id=?",
                            ["true", e.post_id],
                            (err, checkrestric) => {
                              if (err) {
                                return res.status(500).json({
                                  status: 500,
                                  message: "Something went wrong",
                                });
                              }
                            }
                          );
                          return e;
                        } else {
                          if (
                            videoData.data?.items[0]?.snippet?.channelId ==
                              "UCmL1WlDI8UkXDXCXcBQN9CA" ||
                            videoData.data?.items[0]?.snippet?.channelId ==
                              "UCYauDsl-rswjQGpA0_-Z9Pw" ||
                            videoData.data?.items[0]?.snippet?.channelId ==
                              "UCnqA9rYxHah-HY2RzGXWLFg"
                          ) {
                            connection.query(
                              "update post_tbl SET isUltra=? , isrestric=? where id=?",
                              ["true", "true", e.post_id],
                              (err, checkrestric) => {
                                if (err) {
                                  return res.status(500).json({
                                    status: 500,
                                    message: "Something went wrong",
                                  });
                                }
                              }
                            );
                          }
                          connection.query(
                            "update post_tbl SET ismoviecrawl=? where id=?",
                            ["false", e.post_id],
                            (err, checkrestric) => {
                              if (err) {
                                return res.status(500).json({
                                  status: 500,
                                  message: "Something went wrong",
                                });
                              }
                            }
                          );
                        }
                      } catch (error) {
                        console.error("Error fetching video data:", error);
                        if (error.response && error.response.status === 403) {
                          return null;
                        }
                      }
                    }
                  }
                  return null;
                });
                return Promise.all(requests);
              };

              const batchSize = 10;
              const results = [];
              for (let i = 0; i < result.length; i += batchSize) {
                console.log(`Processing batch ${i / batchSize + 1}`);
                const batch = result.slice(i, i + batchSize);
                const batchResults = await batchProcess(batch);
                results.push(...batchResults.filter((e) => e !== null));
              }

              console.log("Total valid results:", results.length);

              if (results.length > 0) {
                // const lastpage = offset + limit >= results.length;
                console.log("Not Supported movie foundsuccesfully ");
                // return res.status(201).json({
                //   status: 1,
                //   message: "Post Found",
                //   result: results,
                //   lastpage,
                //   records: results.length,
                // });
              } else {
                console.log("Not Supported movie not found");
                // return res.status(406).json({
                //   status: 0,
                //   message: "Post Not Found",
                // });
              }
            } else {
              console.log("Not Supported movie not found");
              // return res.status(406).json({
              //   status: 0,
              //   message: "Post Not Found",
              // });
            }
          }
        );
      } catch (err) {
        console.error("Internal Server Error:", err);
      }
    },
    {
      scheduled: true,
      timezone: "Asia/Kolkata",
    }
  );

  cron.schedule(
    "46 17 * * *",
    async () => {
      try {
        console.log("maxvideo apicalled ");
        connection.query(
          "SELECT id, title, link, thumbnail, epno, movieid, created_at FROM mxvideo ORDER BY epno ASC",
          async (err, data) => {
            if (err) {
              return console.error("Error at fetching mxvideo data");
            }

            console.log("data length at mxvideo script", data.length);
            if (data.length > 0) {
              console.log("Data retrieved from database");
              const result = JSON.parse(JSON.stringify(data));

              const batchProcess = async (batch) => {
                console.log("Processing batch of size:", batch.length);
                const requests = batch.map(async (e) => {
                  if (
                    e.link &&
                    e.link.length > 0 &&
                    e.link.includes("dailymotion")
                  ) {
                    const videoid = e.link.split(
                      "https://www.dailymotion.com/video/"
                    );
                    if (videoid && videoid[1]) {
                      try {
                        const videoData = await axios.get(
                          `https://api.dailymotion.com/video/${videoid[1]}`
                        );
                        console.log(
                          "videoid in dailymotion is",
                          videoData.data
                        );

                        if (videoData?.data?.error?.code == 404) {
                          console.log(
                            "Video not found (404) - videoid in dailymotion is",
                            videoData.data
                          );
                          connection.query(
                            "UPDATE mxvideo SET iscrawl=? WHERE id=?",
                            ["true", e.id],
                            (err, result) => {
                              if (err) {
                                console.error(
                                  "Error updating mxvideo (404 case):",
                                  err
                                );
                              } else {
                                console.log(
                                  `Updated record ${e.id} - iscrawl set to true`
                                );
                              }
                            }
                          );
                          return e;
                        } else {
                          connection.query(
                            "UPDATE mxvideo SET iscrawl=? WHERE id=?",
                            ["false", e.id],
                            (err, result) => {
                              if (err) {
                                console.error(
                                  "Error updating mxvideo (success case):",
                                  err
                                );
                              } else {
                                console.log(
                                  `Updated record ${e.id} - iscrawl set to false`
                                );
                              }
                            }
                          );
                        }
                      } catch (error) {
                        console.error(
                          "Error fetching video data from Dailymotion:",
                          error.message
                        );
                        // Handle API errors (403, 404, etc.)
                        if (
                          error?.response?.status === 403 ||
                          error?.response?.status === 404
                        ) {
                          connection.query(
                            "UPDATE mxvideo SET iscrawl=? WHERE id=?",
                            ["true", e.id],
                            (err, result) => {
                              if (err) {
                                console.error(
                                  "Error updating mxvideo (error case):",
                                  err
                                );
                              } else {
                                console.log(
                                  `Updated record ${e.id} - iscrawl set to true (error)`
                                );
                              }
                            }
                          );
                          return e;
                        }
                      }
                    }
                  }
                  return null;
                });
                return Promise.all(requests);
              };

              const batchSize = 10;
              const results = [];
              for (let i = 0; i < result.length; i += batchSize) {
                console.log(`Processing batch ${i / batchSize + 1}`);
                const batch = result.slice(i, i + batchSize);
                const batchResults = await batchProcess(batch);
                results.push(...batchResults.filter((e) => e !== null));
              }

              console.log("Total valid results:", results.length);

              if (results.length > 0) {
                console.log(
                  "Processing completed - found videos to crawl:",
                  results.length
                );
              } else {
                console.log("No videos found that need crawling");
              }
            } else {
              console.log("No data found in mxvideo table");
            }
          }
        );
      } catch (err) {
        console.log("error at dailymotion script", err);
      }
    },
    {
      scheduled: true,
      timezone: "Asia/Kolkata",
    }
  );

  cluster.on("exit", (worker, code, signal) => {
    console.log(`Worker process ${worker.process.pid} died. Restarting...`);
    cluster.fork();
  });
} else {
  const app = express();
  app.use(bodyParser.json());

  var privateKey = fs.readFileSync("./appzone99_science.key", "utf8");
  var certificate = fs.readFileSync("./appzone99_science.crt", "utf8");

  var credentials = { key: privateKey, cert: certificate };
  var httpsServer = https.createServer(credentials, app);

  require("dotenv").config();

  app.use(bodyParser.urlencoded({ extended: true }));
  //app.use(form.array());

  const corsOptions = {
    origin: "*",
    credentials: true,
    optionsSuccessStatus: 200,
    methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
    // referrerPolicy: { policy: "no-referrer-when-downgrade" },
  };

  app.use(cors());

  app.use(cookieParser());
  app.use(
    session({
      secret: "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",
      resave: false,
      saveUninitialized: true,
    })
  );

  app.use(
    session({
      secret: "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",
      resave: false,
      saveUninitialized: true,
    })
  );

  const setImageCacheControl = (req, res, next) => {
    // Set cache control headers only for image requests
    if (req.url.includes("/thumbnail") || req.url.includes("/profile")) {
      res.setHeader("Cache-Control", "public, max-age=31536000"); // 1 year in seconds
    }
    next();
  };

  app.use(setImageCacheControl);
  app.use(passport.initialize());
  app.use(passport.session());
  passport.use(
    "google1",
    new GoogleStrategy(
      {
        clientID:
          "274926775178-6s9ffe31b1re6ekv845drmrgmjch5gil.apps.googleusercontent.com",
        clientSecret: "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",
        callbackURL: "https://appzone99.science:3001/auth/google/callback",
        //   callbackURL: "http://**************:8010/auth/google/callback",
      },
      (accessToken, refreshToken, profile, done) => {
        return done(null, profile.emails[0].value);
      }
    )
  );

  passport.use(
    "google2",
    new GoogleStrategy(
      {
        clientID:
          "576494607076-3kja9rpqkqn4vadlhf12c7rr1lr5bobr.apps.googleusercontent.com",
        clientSecret: "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",
        callbackURL:
          "https://appzone99.science:3001/status/auth/google/callback",
        //   callbackURL: "http://**************:8010/auth/google/callback",
      },
      (accessToken, refreshToken, profile, done) => {
        return done(null, profile.emails[0].value);
      }
    )
  );

  //global path

  app.use("/upload/thumbnail", express.static("uploads/thumbnail"));
  app.use("/upload/profile", express.static("uploads/profile"));
  app.use("/upload/demo", express.static("uploads/demo"));

  // app.use((req, res, next) => {
  //   const ip = req.headers["x-forwarded-for"] || req.connection.remoteAddress;
  //   const geo = lookup(ip);

  //   // Extract the country code
  //   const country = geo ? geo.country : "Unknown";

  //   // Log the API endpoint and the country
  //   console.log(`API call to ${req.originalUrl} from ${JSON.stringify(geo)}`);

  //   // Continue to the next middleware or route handler
  //   next();
  // });

  //for normal user
  app.use(
    "/",
    session({
      key: "user",
      secret: process.env.USER_SECRET,
      resave: true,
      saveUninitialized: false,
    })
  );

  // for admin
  app.use(
    "/adminlogin",
    session({
      key: "admin",
      secret: process.env.ADMIN_SECRET,
      resave: true,
      saveUninitialized: false,
    })
  );
  app.use(router);

  const port = process.env.PORT;
  const host = process.env.HOST;

  passport.serializeUser((user, done) => {
    done(null, user);
  });

  passport.deserializeUser((user, done) => {
    done(null, user);
  });

  // Define routes
  app.get("/login", (req, res) => {
    res.sendFile(__dirname + "/views/moviefliker/login.html");
  });

  app.get("/statussaver", (req, res) => {
    res.sendFile(__dirname + "/views/statusaver/statussaver.html");
  });

  app.get("/", (req, res) => {
    res.send("Getting request...");
  });

  app.set("view engine", "ejs");

  app.get("/deleteacoount", (req, res) => {
    const userEmail = req.session.userEmail;
    res.render("moviefliker/account", { userEmail });
  });

  app.get("/viewmovies", (req, res) => {
    res.render("viewmovies/index");
  });

  app.get("/status/deleteacoount", (req, res) => {
    let userEmail = req.session.userEmail;
    if (userEmail) {
      return res.render("statusaver/status", { userEmail });
    } else {
      res.redirect("/statussaver");
    }
  });

  app.get(
    "/auth/google",
    passport.authenticate("google1", {
      scope: ["https://www.googleapis.com/auth/plus.login", "email"],
    })
  );

  app.get(
    "/auth/google/callback",
    passport.authenticate("google1", { failureRedirect: "/login" }),

    (req, res) => {
      let userEmail = req?.user;
      var ciphertext = base64url.encode(req?.user);
      res.redirect(
        `https://appzone99.science/movieaccount/account.html?userEmail=${ciphertext}`
      );
      //  res.redirect("/deleteacoount");
    }
  );

  app.get(
    "/status/auth/google",
    passport.authenticate("google2", {
      scope: ["https://www.googleapis.com/auth/plus.login", "email"],
    })
  );

  app.get(
    "/status/auth/google/callback",
    passport.authenticate("google2", { failureRedirect: "/login" }),

    (req, res) => {
      let userEmail = req?.user;
      var ciphertext = base64url.encode(req?.user);

      res.redirect(
        `https://appzone99.science/statussaver/status.html?userEmail=${ciphertext}`
      );
    }
  );

  app.get("/deletestatus/:email", async (req, res) => {
    try {
      var email = base64url.decode(req.params.email);
      console.log("emnails", email);
      console.log("users in delete api params", email);
      const deleteApiUrl = "http://socialapp.top:3001/deleteUserData";

      const postData = {
        email: email,
        keyword: "ipUR5Q7PAbWeZt4AIFH9elB8Bko=",
      };

      const response = await axios.post(
        deleteApiUrl,
        querystring.stringify(postData),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );
      res.status(200).json({ message: response.data.data });
    } catch (error) {
      console.error("Error calling Delete API:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  app.delete("/deletedata", async (req, res) => {
    try {
      console.log("Starting authentication...");
      const auth = await authenticate({
        keyfilePath: "./credential.json",
        scopes: SCOPES,
      });
      console.log("Starting authentication...");
      const drive = google.drive({ version: "v3", auth });

      const folderName = "testing";
      let folderId;

      // Find the folder
      const folderRes = await drive.files.list({
        q: `name='${folderName}' and mimeType='application/vnd.google-apps.folder'`,
        fields: "files(id, name)",
        spaces: "drive",
      });

      if (folderRes.data.files.length > 0) {
        folderId = folderRes.data.files[0].id;
      } else {
        return res
          .status(404)
          .json({ status: 404, message: "Folder Not Found" });
      }

      // List files in the folder
      const fileRes = await drive.files.list({
        q: `'${folderId}' in parents`,
        fields: "nextPageToken, files(id, name)",
        spaces: "drive",
      });

      if (fileRes.data.files.length == 0) {
        return res
          .status(404)
          .json({ status: 404, message: "No files found in the folder" });
      }

      const fileData = await drive.files.list({
        q: `'${fileRes.data.files[0].id}' in parents`,
        fields: "nextPageToken, files(id, name)",
        spaces: "drive",
      });

      if (fileData.data.files.length == 0) {
        return res
          .status(404)
          .json({ status: 404, message: "No files found in the folder" });
      }
      for (const file of fileData.data.files) {
        await drive.files.delete({ fileId: file.id });
      }

      return res.status(200).json({
        status: 200,
        message: "Data Deleted Successfully",
      });
    } catch (error) {
      console.error("Error deleting data:", error);
      return res.status(500).json({
        status: 500,
        message: "Something went wrong",
        error: error.message,
      });
    }
  });

  httpsServer.listen(3001);

  app.listen(port, () => {
    console.log(`port is running on ${port}`);
  });
}
